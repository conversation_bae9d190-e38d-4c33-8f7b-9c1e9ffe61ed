#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <time.h>
#include <sys/time.h>
#include <stdarg.h>
#include <sys/stat.h>
#include <errno.h>
#include <unistd.h>
#include <sys/mman.h>
#include <fcntl.h>
#include <pthread.h>
#include <sys/uio.h>
#ifdef __AVX2__
#include <immintrin.h>
#endif

// 系统限制配置
#define MAX_MERCHANTS 100000
#define MAX_RECORDS 25000000
#define MAX_PATH_LENGTH 512

// 时间窗口定义
#define WINDOW_15M (15 * 60)
#define WINDOW_2H (2 * 60 * 60)
#define WINDOW_6H (6 * 60 * 60)

// 性能调优参数
#define HASH_SIZE 131072
#define NUM_THREADS 8
#define CHUNK_SIZE 1000000

// 默认配置
#define DEFAULT_OUTPUT_DIR "."

// 交易记录结构
typedef struct __attribute__((packed)) {
    int mid;
    int trading_time;
    double amount;
    int seqno;
} TradeRecord;

// 窗口统计结果
typedef struct {
    double sum;
    int frequency;
    double avg;
    double stddev;
    double zscore;
    double max;
    double min;
} WindowStats;

// 最终结果记录
typedef struct {
    int seqno;
    WindowStats stats_15m;
    WindowStats stats_2h;
    WindowStats stats_6h;
} ResultRecord;

// 商户数据管理
typedef struct {
    int mid;
    TradeRecord *records;
    int count;
    int capacity;
    int last_15m_start, last_2h_start, last_6h_start;  // 窗口计算缓存位置
} MerchantData;

// 哈希表节点
typedef struct HashNode {
    int mid;
    int merchant_index;
    struct HashNode *next;
} HashNode;

// 线程同步屏障
typedef struct {
    pthread_mutex_t mutex;
    pthread_cond_t cond;
    int count;
    int total;
} ThreadBarrier;

// 线程工作数据
typedef struct {
    int thread_id;
    int start_merchant;
    int end_merchant;
    ThreadBarrier *barrier;
} ThreadData;

// 全局数据存储
TradeRecord *all_records = NULL;
int total_records = 0;
int max_seqno = -1;
MerchantData *merchants = NULL;
int merchant_count = 0;
HashNode *hash_table[HASH_SIZE];
ResultRecord *results = NULL;

// 文件处理
char *file_buffer = NULL;
size_t file_size = 0;
FILE *log_file = NULL;

// 线程同步
pthread_mutex_t hash_mutex = PTHREAD_MUTEX_INITIALIZER;
ThreadBarrier process_barrier;

// 函数声明
int compare_by_time_and_seqno(const void *a, const void *b);
void* process_merchants_thread(void* arg);

// 线程屏障实现
int barrier_init(ThreadBarrier *barrier, int count) {
    barrier->count = 0;
    barrier->total = count;
    pthread_mutex_init(&barrier->mutex, NULL);
    pthread_cond_init(&barrier->cond, NULL);
    return 0;
}

int barrier_wait(ThreadBarrier *barrier) {
    pthread_mutex_lock(&barrier->mutex);
    barrier->count++;
    if (barrier->count == barrier->total) {
        barrier->count = 0;
        pthread_cond_broadcast(&barrier->cond);
        pthread_mutex_unlock(&barrier->mutex);
        return 1;
    } else {
        pthread_cond_wait(&barrier->cond, &barrier->mutex);
        pthread_mutex_unlock(&barrier->mutex);
        return 0;
    }
}

int barrier_destroy(ThreadBarrier *barrier) {
    pthread_mutex_destroy(&barrier->mutex);
    pthread_cond_destroy(&barrier->cond);
    return 0;
}

// 哈希函数
static inline int hash_function(int mid) {
    unsigned int hash = ((unsigned int)mid) * 2654435761U;
    return hash % HASH_SIZE;
}

// 初始化哈希表
void init_hash_table() {
    memset(hash_table, 0, sizeof(hash_table));
}

// 查找商户
MerchantData* find_merchant(int mid) {
    int hash_index = hash_function(mid);
    HashNode *node = hash_table[hash_index];

    while (node) {
        if (node->mid == mid) {
            return &merchants[node->merchant_index];
        }
        node = node->next;
    }
    return NULL;
}

// 添加商户到哈希表
void add_merchant_to_hash(int mid, int merchant_index) {
    int hash_index = hash_function(mid);
    HashNode *new_node = malloc(sizeof(HashNode));
    new_node->mid = mid;
    new_node->merchant_index = merchant_index;
    new_node->next = hash_table[hash_index];
    hash_table[hash_index] = new_node;
}

// 创建目录
int create_directory(const char *path) {
    char tmp[MAX_PATH_LENGTH];
    char *p = NULL;
    size_t len;

    snprintf(tmp, sizeof(tmp), "%s", path);
    len = strlen(tmp);
    if (tmp[len - 1] == '/') {
        tmp[len - 1] = 0;
    }

    for (p = tmp + 1; *p; p++) {
        if (*p == '/') {
            *p = 0;
            if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
                if (errno != EEXIST) {
                    return -1;
                }
            }
            *p = '/';
        }
    }

    if (mkdir(tmp, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH) != 0) {
        if (errno != EEXIST) {
            return -1;
        }
    }

    return 0;
}

// 初始化日志文件
int init_log_file(const char *output_dir) {
    char log_path[MAX_PATH_LENGTH];
    snprintf(log_path, sizeof(log_path), "%s/run.log", output_dir);
    log_file = fopen(log_path, "w");
    if (!log_file) {
        fprintf(stderr, "Cannot create log file: %s\n", log_path);
        return -1;
    }
    printf("Log file created: %s\n", log_path);
    return 0;
}

// 日志输出函数
void log_message(const char *format, ...) {
    va_list args;
    time_t now = time(NULL);
    struct tm *tm_info = localtime(&now);

    printf("[%04d-%02d-%02d %02d:%02d:%02d] ",
           tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
           tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);

    va_start(args, format);
    vprintf(format, args);
    va_end(args);

    if (log_file) {
        fprintf(log_file, "[%04d-%02d-%02d %02d:%02d:%02d] ",
                tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
                tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec);
        va_start(args, format);
        vfprintf(log_file, format, args);
        va_end(args);
        fflush(log_file);
    }
}

// 时间字符串解析 - 使用标准时间解析确保精度
static inline time_t parse_time(const char *time_str) {
    struct tm tm = {0};
    sscanf(time_str, "%d-%d-%d %d:%d:%d",
           &tm.tm_year, &tm.tm_mon, &tm.tm_mday,
           &tm.tm_hour, &tm.tm_min, &tm.tm_sec);
    tm.tm_year -= 1900;  // tm_year是从1900年开始计算
    tm.tm_mon -= 1;      // tm_mon是0-11
    return mktime(&tm);
}

// 查找或创建商户
MerchantData* find_or_create_merchant(int mid) {
    MerchantData *merchant = find_merchant(mid);
    if (merchant) {
        return merchant;
    }

    if (merchant_count >= MAX_MERCHANTS) {
        fprintf(stderr, "Too many merchants\n");
        exit(1);
    }

    merchant = &merchants[merchant_count];
    merchant->mid = mid;
    merchant->records = malloc(sizeof(TradeRecord) * 2000);
    merchant->count = 0;
    merchant->capacity = 2000;
    merchant->last_15m_start = 0;
    merchant->last_2h_start = 0;
    merchant->last_6h_start = 0;

    add_merchant_to_hash(mid, merchant_count);
    merchant_count++;

    return merchant;
}

// 添加记录到商户
void add_record_to_merchant(MerchantData *merchant, TradeRecord *record) {
    if (merchant->count >= merchant->capacity) {
        merchant->capacity *= 2;
        merchant->records = realloc(merchant->records, 
                                  sizeof(TradeRecord) * merchant->capacity);
    }
    merchant->records[merchant->count++] = *record;
}

// 记录排序比较函数
int compare_by_time_and_seqno(const void *a, const void *b) {
    const TradeRecord *ra = (const TradeRecord*)a;
    const TradeRecord *rb = (const TradeRecord*)b;

    if (ra->trading_time < rb->trading_time) return -1;
    if (ra->trading_time > rb->trading_time) return 1;

    return ra->seqno - rb->seqno;
}

// 计算时间窗口内的统计数据
WindowStats calculate_window_stats(MerchantData *merchant, int target_seqno,
                                  int target_time, int window_seconds,
                                  double current_amount, int *last_start) {
    WindowStats stats = {0};

    int window_start = target_time - window_seconds;
    double sum = 0.0, sum_sq = 0.0;
    double min_val = INFINITY, max_val = -INFINITY;
    int count = 0;

    // 二分查找窗口起始位置
    int start_pos = *last_start;
    int left = start_pos, right = merchant->count - 1;

    while (left <= right) {
        int mid = (left + right) / 2;
        TradeRecord *record = &merchant->records[mid];

        if (record->trading_time < window_start || record->seqno > target_seqno) {
            left = mid + 1;
        } else {
            right = mid - 1;
            start_pos = mid;
        }
    }

    *last_start = start_pos;

    // 向量化计算（如果支持AVX2）
    #ifdef __AVX2__
    __m256d sum_vec = _mm256_setzero_pd();
    __m256d sum_sq_vec = _mm256_setzero_pd();

    int i = start_pos;
    for (; i + 3 < merchant->count; i += 4) {
        TradeRecord *records = &merchant->records[i];

        int valid_mask = 0;
        for (int j = 0; j < 4; j++) {
            if (records[j].seqno <= target_seqno &&
                records[j].trading_time <= target_time &&
                records[j].trading_time >= window_start) {
                valid_mask |= (1 << j);
            } else if (records[j].seqno > target_seqno || records[j].trading_time > target_time) {
                break;
            }
        }

        if (valid_mask == 0) break;

        __m256d amounts = _mm256_set_pd(
            (valid_mask & 8) ? records[3].amount : 0.0,
            (valid_mask & 4) ? records[2].amount : 0.0,
            (valid_mask & 2) ? records[1].amount : 0.0,
            (valid_mask & 1) ? records[0].amount : 0.0
        );

        sum_vec = _mm256_add_pd(sum_vec, amounts);
        sum_sq_vec = _mm256_fmadd_pd(amounts, amounts, sum_sq_vec);

        for (int j = 0; j < 4; j++) {
            if (valid_mask & (1 << j)) {
                count++;
                double amount = records[j].amount;
                if (amount < min_val) min_val = amount;
                if (amount > max_val) max_val = amount;
            }
        }
    }

    double sum_array[4], sum_sq_array[4];
    _mm256_storeu_pd(sum_array, sum_vec);
    _mm256_storeu_pd(sum_sq_array, sum_sq_vec);

    for (int j = 0; j < 4; j++) {
        sum += sum_array[j];
        sum_sq += sum_sq_array[j];
    }

    for (; i < merchant->count; i++) {
    #else
    for (int i = start_pos; i < merchant->count; i++) {
    #endif
        TradeRecord *record = &merchant->records[i];

        if (record->seqno > target_seqno) break;
        if (record->trading_time > target_time) break;
        if (record->trading_time < window_start) continue;

        double amount = record->amount;
        sum += amount;
        sum_sq += amount * amount;
        count++;

        if (amount < min_val) min_val = amount;
        if (amount > max_val) max_val = amount;
    }

    if (count == 0) {
        return stats;
    }

    stats.sum = sum;
    stats.frequency = count;
    stats.avg = sum / count;
    stats.max = max_val;
    stats.min = min_val;

    // 计算标准差
    if (count > 1) {
        double variance = (sum_sq - (sum * sum) / count) / (count - 1);
        stats.stddev = sqrt(fmax(0.0, variance));
    } else {
        stats.stddev = 0.0;
    }

    // 计算Z分数
    if (stats.stddev > 0) {
        stats.zscore = (current_amount - stats.avg) / stats.stddev;
    } else {
        stats.zscore = 0.0;
    }

    return stats;
}

// 极速CSV解析 - 专门优化的读取函数
static inline int fast_atoi(const char **ptr, const char *end, char delimiter) {
    int result = 0;
    const char *p = *ptr;
    while (p < end && *p != delimiter && *p != '\n' && *p != '\r') {
        result = result * 10 + (*p - '0');
        p++;
    }
    *ptr = p;
    return result;
}

static inline double fast_atof(const char **ptr, const char *end, char delimiter) {
    double result = 0.0;
    double decimal = 0.1;
    int after_dot = 0;
    const char *p = *ptr;

    while (p < end && *p != delimiter && *p != '\n' && *p != '\r') {
        if (*p == '.') {
            after_dot = 1;
        } else {
            if (after_dot) {
                result += (*p - '0') * decimal;
                decimal *= 0.1;
            } else {
                result = result * 10 + (*p - '0');
            }
        }
        p++;
    }
    *ptr = p;
    return result;
}

// 多线程并行解析CSV
typedef struct {
    char *start;
    char *end;
    int thread_id;
    TradeRecord *records;
    int *record_count;
    pthread_mutex_t *count_mutex;
} ParseThreadData;

void* parse_csv_chunk(void* arg) {
    ParseThreadData *data = (ParseThreadData*)arg;
    char *ptr = data->start;
    char *end = data->end;
    int local_count = 0;
    TradeRecord local_records[CHUNK_SIZE];

    // 找到第一个完整行的开始
    if (ptr != data->start) {
        while (ptr < end && *(ptr-1) != '\n') ptr++;
    }

    while (ptr < end && local_count < CHUNK_SIZE) {
        TradeRecord record;

        // 解析mid
        record.mid = fast_atoi((const char**)&ptr, end, ',');
        if (ptr >= end || *ptr != ',') break;
        ptr++; // 跳过逗号

        // 解析trading_time
        char time_str[32];
        int time_len = 0;
        while (ptr < end && *ptr != ',' && time_len < 31) {
            time_str[time_len++] = *ptr++;
        }
        time_str[time_len] = '\0';
        if (ptr >= end) break;
        record.trading_time = parse_time(time_str);
        ptr++; // 跳过逗号

        // 解析amount
        record.amount = fast_atof((const char**)&ptr, end, ',');
        if (ptr >= end || *ptr != ',') break;
        ptr++; // 跳过逗号

        // 解析seqno
        record.seqno = fast_atoi((const char**)&ptr, end, '\n');

        // 跳过换行符
        while (ptr < end && (*ptr == '\n' || *ptr == '\r')) ptr++;

        local_records[local_count++] = record;
    }

    // 将本地结果复制到全局数组
    pthread_mutex_lock(data->count_mutex);
    int start_index = *data->record_count;
    *data->record_count += local_count;
    pthread_mutex_unlock(data->count_mutex);

    memcpy(&data->records[start_index], local_records, local_count * sizeof(TradeRecord));

    return NULL;
}

// 读取CSV文件并解析数据
int read_csv_file(const char *filename) {
    log_message("Reading CSV file...\n");

    int fd = open(filename, O_RDONLY | O_LARGEFILE);
    if (fd == -1) {
        fprintf(stderr, "Cannot open file: %s\n", filename);
        return -1;
    }

    struct stat st;
    if (fstat(fd, &st) == -1) {
        close(fd);
        return -1;
    }

    file_size = st.st_size;
    log_message("File size: %.2f MB\n", file_size / (1024.0 * 1024.0));

    // 内存映射文件
    file_buffer = mmap(NULL, file_size, PROT_READ, MAP_PRIVATE | MAP_POPULATE, fd, 0);
    close(fd);

    if (file_buffer == MAP_FAILED) {
        fprintf(stderr, "Memory mapping failed\n");
        return -1;
    }

    // 设置顺序访问模式
    #ifdef MADV_SEQUENTIAL
    madvise(file_buffer, file_size, MADV_SEQUENTIAL);
    #endif
    #ifdef MADV_WILLNEED
    madvise(file_buffer, file_size, MADV_WILLNEED);
    #endif

    // 分配内存
    all_records = malloc(sizeof(TradeRecord) * MAX_RECORDS);
    merchants = malloc(sizeof(MerchantData) * MAX_MERCHANTS);
    init_hash_table();

    // 解析CSV数据
    char *ptr = file_buffer;
    char *end = file_buffer + file_size;

    // 跳过标题行
    while (ptr < end && *ptr != '\n') ptr++;
    if (ptr < end) ptr++;

    total_records = 0;

    while (ptr < end && total_records < MAX_RECORDS) {
        TradeRecord record;

        // 解析mid
        record.mid = fast_atoi((const char**)&ptr, end, ',');
        if (ptr >= end || *ptr != ',') break;
        ptr++; // 跳过逗号

        // 解析trading_time
        char time_str[32];
        int time_len = 0;
        while (ptr < end && *ptr != ',' && time_len < 31) {
            time_str[time_len++] = *ptr++;
        }
        time_str[time_len] = '\0';
        if (ptr >= end) break;
        record.trading_time = parse_time(time_str);
        ptr++; // 跳过逗号

        // 解析amount
        record.amount = fast_atof((const char**)&ptr, end, ',');
        if (ptr >= end || *ptr != ',') break;
        ptr++; // 跳过逗号

        // 解析seqno
        record.seqno = fast_atoi((const char**)&ptr, end, '\n');

        // 跳过换行符
        while (ptr < end && (*ptr == '\n' || *ptr == '\r')) ptr++;

        all_records[total_records] = record;

        // 更新最大seqno
        if (record.seqno > max_seqno) {
            max_seqno = record.seqno;
        }

        total_records++;
    }

    log_message("Parsed %d records using %d threads\n", total_records, NUM_THREADS);
    log_message("Max seqno found: %d\n", max_seqno);

    // 分配结果数组，大小基于最大seqno
    int result_array_size = max_seqno + 1;
    results = malloc(sizeof(ResultRecord) * result_array_size);
    if (!results) {
        log_message("ERROR: Failed to allocate memory for results array\n");
        return -1;
    }
    memset(results, 0, sizeof(ResultRecord) * result_array_size);
    log_message("Allocated results array for %d records\n", result_array_size);

    // 5. 构建商户数据结构
    for (int i = 0; i < total_records; i++) {
        MerchantData *merchant = find_or_create_merchant(all_records[i].mid);
        add_record_to_merchant(merchant, &all_records[i]);
    }

    // 6. 并行排序商户数据
    log_message("Sorting records for %d merchants...\n", merchant_count);
    #pragma omp parallel for
    for (int i = 0; i < merchant_count; i++) {
        MerchantData *merchant = &merchants[i];
        qsort(merchant->records, merchant->count, sizeof(TradeRecord), compare_by_time_and_seqno);
    }

    return total_records;
}

// 多线程处理商户数据
void* process_merchants_thread(void* arg) {
    ThreadData *data = (ThreadData*)arg;

    for (int m = data->start_merchant; m < data->end_merchant; m++) {
        MerchantData *merchant = &merchants[m];

        // 重置缓存位置
        merchant->last_15m_start = 0;
        merchant->last_2h_start = 0;
        merchant->last_6h_start = 0;

        // 为该商户的所有记录计算窗口统计
        for (int i = 0; i < merchant->count; i++) {
            TradeRecord *record = &merchant->records[i];
            int seqno = record->seqno;

            // 检查seqno是否在有效范围内
            if (seqno < 0 || seqno > max_seqno) {
                log_message("ERROR: Invalid seqno %d for merchant %d (max_seqno=%d)\n",
                           seqno, merchant->mid, max_seqno);
                continue;
            }

            // 计算三个时间窗口的统计数据
            results[seqno].seqno = seqno;
            results[seqno].stats_15m = calculate_window_stats(
                merchant, seqno, record->trading_time, WINDOW_15M, record->amount, &merchant->last_15m_start);
            results[seqno].stats_2h = calculate_window_stats(
                merchant, seqno, record->trading_time, WINDOW_2H, record->amount, &merchant->last_2h_start);
            results[seqno].stats_6h = calculate_window_stats(
                merchant, seqno, record->trading_time, WINDOW_6H, record->amount, &merchant->last_6h_start);
        }

        // 每处理1000个商户报告进度
        if ((m - data->start_merchant) % 1000 == 0) {
            log_message("Thread %d: processed %d/%d merchants\n",
                       data->thread_id, m - data->start_merchant + 1,
                       data->end_merchant - data->start_merchant);
        }
    }

    return NULL;
}

// 多线程处理所有记录
void process_all_records() {
    log_message("Processing records with %d threads...\n", NUM_THREADS);

    // 结果数组已经在read_csv_file中初始化了

    // 初始化线程屏障
    barrier_init(&process_barrier, NUM_THREADS);

    // 创建线程
    pthread_t threads[NUM_THREADS];
    ThreadData thread_data[NUM_THREADS];

    int merchants_per_thread = merchant_count / NUM_THREADS;
    int remaining_merchants = merchant_count % NUM_THREADS;

    for (int i = 0; i < NUM_THREADS; i++) {
        thread_data[i].thread_id = i;
        thread_data[i].start_merchant = i * merchants_per_thread;
        thread_data[i].end_merchant = (i + 1) * merchants_per_thread;

        // 最后一个线程处理剩余的商户
        if (i == NUM_THREADS - 1) {
            thread_data[i].end_merchant += remaining_merchants;
        }

        thread_data[i].barrier = &process_barrier;



        if (pthread_create(&threads[i], NULL, process_merchants_thread, &thread_data[i]) != 0) {
            fprintf(stderr, "Failed to create thread %d\n", i);
            exit(1);
        }
    }

    // 等待所有线程完成
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_join(threads[i], NULL);
    }

    barrier_destroy(&process_barrier);
    log_message("All threads completed processing\n");

    // 检查结果数组的完整性
    int valid_results = 0;
    int missing_results = 0;
    int first_missing = -1;
    int last_valid = -1;

    for (int i = 0; i <= max_seqno; i++) {
        if (results[i].seqno == i) {
            valid_results++;
            last_valid = i;
        } else {
            missing_results++;
            if (first_missing == -1) {
                first_missing = i;
            }
            if (missing_results <= 10) {  // 只打印前10个缺失的
                log_message("Missing result for seqno %d\n", i);
            }
        }
    }
    log_message("Valid results: %d, Missing results: %d\n", valid_results, missing_results);
    if (first_missing != -1) {
        log_message("First missing seqno: %d, Last valid seqno: %d\n", first_missing, last_valid);
    }
}

// 超高性能结果写入 - 使用多种优化技术
typedef struct {
    int start_record;
    int end_record;
    char *buffer;
    size_t buffer_size;
    size_t *written_size;
    pthread_mutex_t *size_mutex;
} WriteThreadData;

void* format_results_chunk(void* arg) {
    WriteThreadData *data = (WriteThreadData*)arg;
    char *buffer = data->buffer;
    size_t pos = 0;
    size_t buffer_size = data->buffer_size;
    int records_written = 0;

    for (int i = data->start_record; i < data->end_record; i++) {
        // 检查这个索引是否超出最大seqno范围
        if (i > max_seqno) {
            break;
        }

        ResultRecord *r = &results[i];

        // 检查这是否是一个有效的结果记录
        // 有效记录应该有正确的seqno（等于索引i）
        if (r->seqno != i) {
            continue;
        }

        // 检查缓冲区剩余空间（每行最多500字节安全边距）
        if (pos + 500 >= buffer_size) {
            fprintf(stderr, "Buffer near full in thread, stopping at record %d (pos=%zu, size=%zu)\n",
                    i, pos, buffer_size);
            break;
        }

        // 使用更安全的格式化方法
        int written = snprintf(buffer + pos, buffer_size - pos,
            "%d,%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,"
            "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f,"
            "%.6f,%d,%.6f,%.6f,%.6f,%.6f,%.6f\n",
            r->seqno, r->stats_15m.sum, r->stats_15m.frequency, r->stats_15m.avg,
            r->stats_15m.stddev, r->stats_15m.zscore, r->stats_15m.max, r->stats_15m.min,
            r->stats_2h.sum, r->stats_2h.frequency, r->stats_2h.avg,
            r->stats_2h.stddev, r->stats_2h.zscore, r->stats_2h.max, r->stats_2h.min,
            r->stats_6h.sum, r->stats_6h.frequency, r->stats_6h.avg,
            r->stats_6h.stddev, r->stats_6h.zscore, r->stats_6h.max, r->stats_6h.min);

        if (written < 0 || written >= (buffer_size - pos)) {
            fprintf(stderr, "snprintf failed or buffer too small at record %d\n", i);
            break;
        }

        pos += written;
        records_written++;
    }

    pthread_mutex_lock(data->size_mutex);
    *data->written_size += pos;
    pthread_mutex_unlock(data->size_mutex);



    return NULL;
}

void write_results(const char *filename) {
    log_message("Writing results...\n");

    // 使用max_seqno+1作为总记录数
    int total_result_records = max_seqno + 1;
    log_message("Total result records to write: %d\n", total_result_records);

    // 计算每个线程处理的记录数
    int records_per_thread = total_result_records / NUM_THREADS;
    size_t bytes_per_record = 350; // 实际测试每行约280-350字节
    size_t base_buffer_size = (size_t)records_per_thread * bytes_per_record;
    size_t safety_margin = 20 * 1024 * 1024; // 20MB安全边距
    size_t thread_buffer_size = base_buffer_size + safety_margin;

    // 确保不超过可用内存（保守使用8GB）
    size_t max_total_memory = 8LL * 1024 * 1024 * 1024; // 8GB
    size_t total_buffer_memory = thread_buffer_size * NUM_THREADS;

    if (total_buffer_memory > max_total_memory) {
        thread_buffer_size = max_total_memory / NUM_THREADS;
        log_message("Adjusted buffer size to fit memory constraints\n");
    }

    log_message("Records per thread: %d\n", records_per_thread);
    log_message("Thread buffer size: %.2f MB\n", thread_buffer_size / (1024.0 * 1024.0));
    log_message("Total buffer memory: %.2f MB\n", (thread_buffer_size * NUM_THREADS) / (1024.0 * 1024.0));

    // 多线程并行格式化数据
    pthread_t format_threads[NUM_THREADS];
    WriteThreadData write_data[NUM_THREADS];
    char *thread_buffers[NUM_THREADS];
    size_t total_written = 0;
    pthread_mutex_t size_mutex = PTHREAD_MUTEX_INITIALIZER;

    // 分配线程缓冲区
    log_message("Allocating buffers for %d threads...\n", NUM_THREADS);
    for (int i = 0; i < NUM_THREADS; i++) {
        log_message("Allocating buffer %d (%.2f MB)...\n", i, thread_buffer_size / (1024.0 * 1024.0));
        thread_buffers[i] = malloc(thread_buffer_size);
        if (!thread_buffers[i]) {
            log_message("ERROR: Failed to allocate %.2f MB buffer for thread %d\n",
                       thread_buffer_size / (1024.0 * 1024.0), i);
            log_message("Available memory before allocation:\n");
            system("free -h");

            // 清理已分配的缓冲区
            for (int j = 0; j < i; j++) {
                free(thread_buffers[j]);
            }
            exit(1);
        }

        write_data[i].start_record = i * records_per_thread;
        write_data[i].end_record = (i == NUM_THREADS - 1) ? total_result_records : (i + 1) * records_per_thread;
        write_data[i].buffer = thread_buffers[i];
        write_data[i].buffer_size = thread_buffer_size;
        write_data[i].written_size = &total_written;
        write_data[i].size_mutex = &size_mutex;

        log_message("Thread %d: records %d to %d\n", i, write_data[i].start_record, write_data[i].end_record - 1);
    }
    log_message("All buffers allocated successfully\n");

    // 启动格式化线程
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_create(&format_threads[i], NULL, format_results_chunk, &write_data[i]);
    }

    // 等待格式化完成
    for (int i = 0; i < NUM_THREADS; i++) {
        pthread_join(format_threads[i], NULL);
    }

    log_message("Data formatting completed, total size: %.2f MB\n", total_written / (1024.0 * 1024.0));

    // 3. 使用高性能写入
    int fd = open(filename, O_WRONLY | O_CREAT | O_TRUNC | O_LARGEFILE, 0644);
    if (fd == -1) {
        fprintf(stderr, "ERROR: Cannot create output file: %s\n", filename);
        exit(1);
    }

    // 预分配文件空间
    if (posix_fallocate(fd, 0, total_written) != 0) {
        log_message("Warning: Could not preallocate file space\n");
    }

    // 写入标题行
    const char *header = "seqno,sum_15m,frequency_15m,avg_15m,stddev_15m,zscore_15m,max_15m,min_15m,"
                         "sum_2h,frequency_2h,avg_2h,stddev_2h,zscore_2h,max_2h,min_2h,"
                         "sum_6h,frequency_6h,avg_6h,stddev_6h,zscore_6h,max_6h,min_6h\n";
    write(fd, header, strlen(header));

    // 4. 使用writev进行向量化写入
    struct iovec iov[NUM_THREADS];
    size_t total_iov_len = 0;
    for (int i = 0; i < NUM_THREADS; i++) {
        iov[i].iov_base = thread_buffers[i];
        iov[i].iov_len = strlen(thread_buffers[i]);
        total_iov_len += iov[i].iov_len;

    }
    log_message("Total iov length: %zu bytes\n", total_iov_len);

    // 分批写入以避免2GB限制
    ssize_t total_written_bytes = 0;
    const size_t MAX_WRITE_SIZE = 1024 * 1024 * 1024; // 1GB per batch

    for (int i = 0; i < NUM_THREADS; i++) {
        ssize_t written = write(fd, iov[i].iov_base, iov[i].iov_len);
        if (written == -1) {
            fprintf(stderr, "ERROR: Write failed for thread %d\n", i);
            exit(1);
        }
        if (written != (ssize_t)iov[i].iov_len) {
            fprintf(stderr, "ERROR: Partial write for thread %d: %zd/%zu bytes\n",
                    i, written, iov[i].iov_len);
            exit(1);
        }
        total_written_bytes += written;

    }

    log_message("Total written: %zd bytes (expected %zu)\n", total_written_bytes, total_iov_len);

    // 5. 强制同步到磁盘
    fsync(fd);
    close(fd);

    // 清理缓冲区
    for (int i = 0; i < NUM_THREADS; i++) {
        free(thread_buffers[i]);
    }
    pthread_mutex_destroy(&size_mutex);

    log_message("Results written to %s (%.2f MB)\n",
               filename, total_written_bytes / (1024.0 * 1024.0));
}

// 清理内存
void cleanup() {
    if (file_buffer && file_buffer != MAP_FAILED) {
        munmap(file_buffer, file_size);
        file_buffer = NULL;
    }

    if (all_records) {
        free(all_records);
        all_records = NULL;
    }

    if (results) {
        free(results);
        results = NULL;
    }

    if (merchants) {
        for (int i = 0; i < merchant_count; i++) {
            if (merchants[i].records) {
                free(merchants[i].records);
            }
        }
        free(merchants);
        merchants = NULL;
    }

    // 清理哈希表
    for (int i = 0; i < HASH_SIZE; i++) {
        HashNode *node = hash_table[i];
        while (node) {
            HashNode *next = node->next;
            free(node);
            node = next;
        }
        hash_table[i] = NULL;
    }

    if (log_file) {
        fclose(log_file);
        log_file = NULL;
    }
}

// 主函数
int main(int argc, char *argv[]) {
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <input_csv_file> [output_directory]\n", argv[0]);
        return 1;
    }

    const char *input_file = argv[1];
    const char *output_dir = (argc >= 3) ? argv[2] : DEFAULT_OUTPUT_DIR;

    // 创建输出目录
    if (create_directory(output_dir) != 0) {
        fprintf(stderr, "Failed to create output directory: %s\n", output_dir);
        return 1;
    }

    // 初始化日志文件
    if (init_log_file(output_dir) != 0) {
        return 1;
    }

    // 生成输出文件路径
    char output_file[MAX_PATH_LENGTH];
    snprintf(output_file, sizeof(output_file), "%s/result.csv", output_dir);

    log_message("Starting high-performance calculation (v6)...\n");
    log_message("Input file: %s\n", input_file);
    log_message("Output directory: %s\n", output_dir);
    log_message("Using %d threads on 8c16G environment\n", NUM_THREADS);

    struct timeval start_time, read_time, process_time, end_time;
    gettimeofday(&start_time, NULL);

    // 读取数据
    log_message("Reading data...\n");
    if (read_csv_file(input_file) < 0) {
        log_message("ERROR: Failed to read input file\n");
        cleanup();
        return 1;
    }

    gettimeofday(&read_time, NULL);
    double read_duration = (read_time.tv_sec - start_time.tv_sec) +
                          (read_time.tv_usec - start_time.tv_usec) / 1000000.0;
    log_message("Data reading completed in %.4f seconds\n", read_duration);

    // 处理所有记录
    log_message("Starting processing...\n");
    process_all_records();

    gettimeofday(&process_time, NULL);
    double process_duration = (process_time.tv_sec - read_time.tv_sec) +
                             (process_time.tv_usec - read_time.tv_usec) / 1000000.0;
    log_message("Data processing completed in %.4f seconds\n", process_duration);

    // 写入结果
    log_message("Writing results...\n");
    write_results(output_file);

    gettimeofday(&end_time, NULL);
    double write_duration = (end_time.tv_sec - process_time.tv_sec) +
                           (end_time.tv_usec - process_time.tv_usec) / 1000000.0;
    double total_duration = (end_time.tv_sec - start_time.tv_sec) +
                           (end_time.tv_usec - start_time.tv_usec) / 1000000.0;

    log_message("Results writing completed in %.4f seconds\n", write_duration);
    log_message("Total execution time: %.4f seconds\n", total_duration);
    log_message("Processed %d records from %d merchants\n", total_records, merchant_count);
    log_message("Performance: %.0f records/second\n", total_records / total_duration);
    log_message("Memory usage optimized for 8c16G environment\n");
    log_message("Calculation completed successfully!\n");

    // 清理内存
    cleanup();

    return 0;
}
